/**
 * @fileoverview SpreadsheetsAgent - Simplified agentic workflow for Google Sheets operations using LangGraph
 */

import { StateGraph, START, END } from '@langchain/langgraph';
import { HumanMessage, AIMessage, SystemMessage } from '@langchain/core/messages';
import { tool } from '@langchain/core/tools';
import { Annotation } from '@langchain/langgraph';
import { DriveUtilityApp } from './DriveUtilityApp.js';
import { LLMService } from './LLMService.js';
import logger from '../config/logger.js';

/**
 * Simple Agent State - following reference pattern
 */
const AgentState = Annotation.Root({
  messages: Annotation({
    reducer: (x, y) => x.concat(y),
    default: () => []
  }),
  userId: Annotation({
    reducer: (x, y) => y ?? x,
    default: () => ''
  })
});

/**
 * SpreadsheetsReActAgent Class - Simplified version following reference pattern
 * A ReAct agent for Google Sheets operations using LangGraph
 */
export class SpreadsheetsAgent {
  constructor() {
    this.tools = this._getTools();
    this.graph = this._createGraph();
  }

  /**
   * Define the tools for the agent - simplified without complex schemas
   * @returns {Array} Array of LangGraph tools
   * @private
   */
  _getTools() {
    const listSheets = tool(
      async (args) => {
        // userId will be injected by the custom tools node
        const userId = args.userId;
        try {
          const result = await DriveUtilityApp.getAllSpreadsheets(userId);
          if (result && result.spreadsheets) {
            return JSON.stringify({ sheets: result.spreadsheets }, null, 2);
          } else {
            return JSON.stringify({ error: "Failed to retrieve sheets" }, null, 2);
          }
        } catch (error) {
          return JSON.stringify({ error: `Error listing sheets: ${error.message}` }, null, 2);
        }
      },
      {
        name: 'list_sheets',
        description: 'Get all Google Sheets available to the user. Returns JSON string containing list of sheets with their IDs and names'
      }
    );

    const getSheetMetadata = tool(
      async (args) => {
        const { userId, spreadsheetId } = args;
        try {
          const metadata = await DriveUtilityApp.getAvailableSheets(userId, spreadsheetId);
          return JSON.stringify(metadata, null, 2);
        } catch (error) {
          return JSON.stringify({ error: `Error getting sheet metadata: ${error.message}` }, null, 2);
        }
      },
      {
        name: 'get_sheet_metadata',
        description: 'Get metadata for a specific Google Sheet including sheet names, IDs, and properties. Args: spreadsheetId (str)'
      }
    );

    const getSheetData = tool(
      async (args) => {
        const { userId, spreadsheetId, sheetName, range } = args;
        try {
          const options = {};
          if (sheetName) options.sheetName = sheetName;
          if (range) options.range = range;

          const data = await DriveUtilityApp.readSpreadsheetContent(userId, spreadsheetId, options);
          return JSON.stringify({ data: data }, null, 2);
        } catch (error) {
          return JSON.stringify({ error: `Error getting sheet data: ${error.message}` }, null, 2);
        }
      },
      {
        name: 'get_sheet_data',
        description: 'Get the actual data/content from a specific sheet within a spreadsheet. Args: spreadsheetId (str), sheetName (str, optional), range (str, optional)'
      }
    );

    const createNewSheet = tool(
      async (args) => {
        const { userId, sheetName } = args;
        try {
          const result = await DriveUtilityApp.createSpreadsheet(userId, sheetName);
          return JSON.stringify(result, null, 2);
        } catch (error) {
          return JSON.stringify({ error: `Error creating sheet: ${error.message}` }, null, 2);
        }
      },
      {
        name: 'create_new_sheet',
        description: 'Create a new Google Sheet with the specified name. Args: sheetName (str)'
      }
    );

    const writeDataToSheet = tool(
      async (args) => {
        const { userId, spreadsheetId, sheetName, data, rangeStart = "A1" } = args;
        try {
          // Parse the data string into a 2D list
          let parsedData;
          try {
            parsedData = JSON.parse(data);
            if (!Array.isArray(parsedData)) {
              return JSON.stringify({ error: "Data must be a 2D array (list of lists)" }, null, 2);
            }
          } catch (parseError) {
            return JSON.stringify({ error: "Invalid JSON format for data parameter" }, null, 2);
          }

          const result = await DriveUtilityApp.updateSpreadsheetContent(
            userId,
            spreadsheetId,
            sheetName,
            rangeStart,
            parsedData
          );
          return JSON.stringify(result, null, 2);
        } catch (error) {
          return JSON.stringify({ error: `Error writing to sheet: ${error.message}` }, null, 2);
        }
      },
      {
        name: 'write_data_to_sheet',
        description: 'Write data to a specific sheet in a Google Spreadsheet. Args: spreadsheetId (str), sheetName (str), data (str - JSON string representing 2D array), rangeStart (str, default: "A1")'
      }
    );

    return [listSheets, getSheetMetadata, getSheetData, createNewSheet, writeDataToSheet];
  }

  /**
   * Create the LangGraph workflow - simplified version following reference pattern
   * @private
   */
  _createGraph() {
    // Create the graph
    const workflow = new StateGraph(AgentState);

    // Add nodes
    workflow.addNode("agent", this._agentNode.bind(this));

    // Add custom tools node that passes userId from state
    if (this.tools) {
      workflow.addNode("tools", this._toolsNode.bind(this));
    }

    // Add edges
    workflow.addEdge(START, "agent");

    if (this.tools) {
      // Conditional edge from agent to tools or end
      workflow.addConditionalEdges(
        "agent",
        this._shouldContinue.bind(this),
        {
          "continue": "tools",
          "end": END
        }
      );
      // Edge from tools back to agent
      workflow.addEdge("tools", "agent");
    } else {
      // If no tools, go directly to end
      workflow.addEdge("agent", END);
    }

    // Compile the graph
    return workflow.compile();
  }

  /**
   * The main agent node that processes messages and decides on actions
   * @param {Object} state - Current state
   * @returns {Object} Updated state
   * @private
   */
  async _agentNode(state) {
    const messages = state.messages;

    if (this.tools) {
      // Bind tools to the LLM for tool calling
      const model = await LLMService.getModelWithDatabaseApiKey('gpt-4o');
      const llmWithTools = model.bindTools(this.tools);
      const response = await llmWithTools.invoke(messages);
      console.log(response);
      return { messages: [response] };
    } else {
      // No tools available, just respond
      const model = await LLMService.getModelWithDatabaseApiKey('gpt-4o');
      const response = await model.invoke(messages);
      return { messages: [response] };
    }
  }

  /**
   * Custom tools node that injects userId from state
   * @param {Object} state - Current state
   * @returns {Object} Updated state
   * @private
   */
  async _toolsNode(state) {
    const messages = state.messages;
    const lastMessage = messages[messages.length - 1];
    const userId = state.userId;

    if (!lastMessage.tool_calls || lastMessage.tool_calls.length === 0) {
      return { messages: [] };
    }

    const toolMessages = [];

    for (const toolCall of lastMessage.tool_calls) {
      const tool = this.tools.find(t => t.name === toolCall.name);
      if (tool) {
        try {
          // Inject userId into tool arguments
          const toolArgs = { ...toolCall.args, userId };
          const result = await tool.invoke(toolArgs);

          toolMessages.push({
            type: 'tool',
            tool_call_id: toolCall.id,
            name: toolCall.name,
            content: result
          });
        } catch (error) {
          toolMessages.push({
            type: 'tool',
            tool_call_id: toolCall.id,
            name: toolCall.name,
            content: JSON.stringify({ error: `Error executing tool: ${error.message}` })
          });
        }
      } else {
        toolMessages.push({
          type: 'tool',
          tool_call_id: toolCall.id,
          name: toolCall.name,
          content: JSON.stringify({ error: `Tool ${toolCall.name} not found` })
        });
      }
    }

    return { messages: toolMessages };
  }

  /**
   * Determine whether to continue to tools or end
   * @param {Object} state - Current state
   * @returns {string} Next action
   * @private
   */
  _shouldContinue(state) {
    const messages = state.messages;
    const lastMessage = messages[messages.length - 1];

    // If the last message has tool calls, continue to tools
    if (lastMessage && lastMessage.tool_calls && lastMessage.tool_calls.length > 0) {
      return "continue";
    } else {
      return "end";
    }
  }

  /**
   * Run the agent with user input - simplified version following reference pattern
   * @param {string} userId - User ID
   * @param {string} userInput - User input message
   * @returns {Promise<string>} Agent response
   */
  async run(userId, userInput) {
    // Create initial state with system message and user input
    const systemMessage = new SystemMessage(`You are a Google Sheets assistant agent. Your role is to help users interact with their Google Sheets data.

You have access to the following tools:
1. list_sheets - Get all available Google Sheets for the user
2. get_sheet_metadata - Get detailed information about a specific spreadsheet including sheet names and IDs
3. get_sheet_data - Get the actual content/data from a specific sheet
4. create_new_sheet - Create a new Google Sheet with a specified name
5. write_data_to_sheet - Write data to a specific sheet in a spreadsheet

Based on the user's query, you should:
- Use list_sheets when they want to see what sheets are available
- Use get_sheet_metadata when they need information about a specific spreadsheet's structure
- Use get_sheet_data when they want to see the actual content of a sheet
- Use create_new_sheet when they want to create a new spreadsheet
- Use write_data_to_sheet when they want to add data to an existing sheet
- Always provide helpful explanations of the data you retrieve
- If you need a spreadsheet_id that the user hasn't provided, ask them to specify it or help them find it using the available tools
- When writing data, ensure the data is properly formatted as a 2D array (JSON string format)

Be conversational and helpful in your responses.

User ID: ${userId}`);

    const initialState = {
      messages: [systemMessage, new HumanMessage(userInput)],
      userId
    };

    // Run the graph
    const result = await this.graph.invoke(initialState);

    // Return the last AI message
    const lastMessage = result.messages[result.messages.length - 1];
    return lastMessage.content || String(lastMessage);
  }

  /**
   * Process a user request through the agent workflow - simplified wrapper
   * @param {string} userId - User ID
   * @param {string} message - User message/request
   * @param {Object} context - Additional context (optional)
   * @param {string} sessionId - Optional session ID for tracking
   * @returns {Promise<Object>} Agent response
   */
  async processRequest(userId, message, context = {}, sessionId = null) {
    try {
      logger.info(`Processing spreadsheet agent request for user: ${userId}${sessionId ? `, sessionId: ${sessionId}` : ''}`);

      const response = await this.run(userId, message);

      return {
        success: true,
        message: response,
        data: null
      };
    } catch (error) {
      logger.error('Error processing agent request:', error);
      return {
        success: false,
        error: 'AGENT_ERROR',
        message: 'An error occurred while processing your request.',
        details: error.message
      };
    }
  }

  /**
   * Get agent status and health check
   * @returns {Object} Agent status
   */
  getStatus() {
    return {
      status: 'healthy',
      tools: this.tools.map(tool => ({
        name: tool.name,
        description: tool.description
      })),
      graphInitialized: !!this.graph,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Reset agent state (useful for testing)
   */
  reset() {
    logger.info('Resetting SpreadsheetsAgent');
    this.graph = this._createGraph();
  }
}
